/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as vscode from 'vscode';
import { CancellationToken, LanguageModelToolInvocationOptions, LanguageModelToolResult } from 'vscode';
import { LanguageModelTextPart } from '../../../util/vs/base/common/languageModels';
import { checkCancellation } from '../../../util/vs/base/common/cancellation';
import { IWorkspaceService } from '../../../platform/workspace/common/workspaceService';
import { IFileSystemService } from '../../../platform/filesystem/common/fileSystemService';
import { IProjectTagsService } from '../../projectTags/common/projectTagsService';
import { IGetProjectTagsRequest, ProjectTagType } from '../../projectTags/common/projectTagsTypes';
import { ToolName } from '../common/toolNames';
import { ToolRegistry } from '../common/toolsRegistry';

interface IGetProjectTagsToolParams {
	/** Specific tag IDs to retrieve */
	tagIds?: string[];
	/** Filter by tag type */
	type?: ProjectTagType;
	/** Filter by keywords (comma-separated) */
	keywords?: string;
	/** Maximum number of tags to return */
	limit?: number;
	/** Include file contents in response */
	includeContent?: boolean;
	/** Search query to find relevant tags */
	query?: string;
}

/**
 * Tool for retrieving project tags and their associated content
 */
class GetProjectTagsTool implements vscode.LanguageModelTool<IGetProjectTagsToolParams> {
	public static readonly toolName = ToolName.GetProjectTags;

	constructor(
		@IWorkspaceService private readonly workspaceService: IWorkspaceService,
		@IProjectTagsService private readonly projectTagsService: IProjectTagsService,
		@IFileSystemService private readonly fileSystemService: IFileSystemService
	) {}

	async invoke(
		options: LanguageModelToolInvocationOptions<IGetProjectTagsToolParams>,
		token: CancellationToken
	): Promise<LanguageModelToolResult> {
		checkCancellation(token);

		const {
			tagIds,
			type,
			keywords,
			limit = 10,
			includeContent = false,
			query
		} = options.input;

		// Get the current workspace
		const workspaceFolders = this.workspaceService.workspaceFolders;
		if (!workspaceFolders || workspaceFolders.length === 0) {
			return new LanguageModelToolResult([
				new LanguageModelTextPart('No workspace is currently open. Please open a workspace first.')
			]);
		}

		const workspaceRoot = workspaceFolders[0].uri;

		try {
			// Check if project is initialized
			const isInitialized = await this.projectTagsService.isProjectInitialized(workspaceRoot);
			if (!isInitialized) {
				return new LanguageModelToolResult([
					new LanguageModelTextPart(
						'Project has not been initialized yet. Please use the @initProject tool first to analyze the project structure and generate tags.'
					)
				]);
			}

			let tags;

			// If query is provided, use search
			if (query) {
				tags = await this.projectTagsService.searchTags(query, workspaceRoot, token);
			} else {
				// Create request based on parameters
				const request: IGetProjectTagsRequest = {
					tagIds,
					type,
					keywords: keywords ? keywords.split(',').map(k => k.trim()) : undefined,
					limit,
					includeContent
				};

				const response = await this.projectTagsService.getProjectTags(request, token);
				tags = response.tags;
			}

			if (tags.length === 0) {
				return new LanguageModelToolResult([
					new LanguageModelTextPart('No tags found matching the specified criteria.')
				]);
			}

			// Format the response
			let result = `Found ${tags.length} matching tag(s):\n\n`;

			for (const tag of tags) {
				result += `## ${tag.name} (${tag.type})\n`;
				result += `**ID:** ${tag.id}\n`;
				result += `**Description:** ${tag.description}\n`;
				result += `**Priority:** ${tag.priority}\n`;
				
				if (tag.keywords.length > 0) {
					result += `**Keywords:** ${tag.keywords.join(', ')}\n`;
				}
				
				if (tag.dependencies.length > 0) {
					result += `**Dependencies:** ${tag.dependencies.join(', ')}\n`;
				}
				
				result += `**Files:** ${tag.filePaths.length} file(s)\n`;
				tag.filePaths.slice(0, 5).forEach(filePath => {
					result += `- ${filePath}\n`;
				});
				
				if (tag.filePaths.length > 5) {
					result += `... and ${tag.filePaths.length - 5} more files\n`;
				}

				// Include file contents if requested
				if (includeContent && tag.filePaths.length > 0) {
					result += `\n**File Contents:**\n`;
					
					// Limit to first 3 files to avoid overwhelming response
					const filesToShow = tag.filePaths.slice(0, 3);
					
					for (const filePath of filesToShow) {
						try {
							const fileUri = workspaceRoot.with({ path: workspaceRoot.path + '/' + filePath });
							const exists = await this.fileSystemService.exists(fileUri);
							
							if (exists) {
								const content = await this.fileSystemService.readFile(fileUri);
								const text = content.toString('utf8');
								
								// Limit content size
								const maxContentLength = 2000;
								const truncatedText = text.length > maxContentLength 
									? text.substring(0, maxContentLength) + '\n... (truncated)'
									: text;
								
								result += `\n### ${filePath}\n`;
								result += '```\n';
								result += truncatedText;
								result += '\n```\n';
							}
						} catch (error) {
							result += `\n### ${filePath}\n`;
							result += `Error reading file: ${error instanceof Error ? error.message : String(error)}\n`;
						}
					}
					
					if (tag.filePaths.length > 3) {
						result += `\n... and ${tag.filePaths.length - 3} more files not shown\n`;
					}
				}

				if (tag.metadata && Object.keys(tag.metadata).length > 0) {
					result += `**Metadata:**\n`;
					for (const [key, value] of Object.entries(tag.metadata)) {
						result += `- ${key}: ${JSON.stringify(value)}\n`;
					}
				}

				result += `**Last Updated:** ${tag.updatedAt.toISOString()}\n\n`;
				result += '---\n\n';
			}

			// Add usage tips
			result += `\n**Tips:**\n`;
			result += `- Use includeContent=true to see file contents\n`;
			result += `- Use type parameter to filter by tag type (${Object.values(ProjectTagType).join(', ')})\n`;
			result += `- Use keywords parameter to filter by specific keywords\n`;
			result += `- Use query parameter to search tags by natural language\n`;

			return new LanguageModelToolResult([
				new LanguageModelTextPart(result)
			]);

		} catch (error) {
			return new LanguageModelToolResult([
				new LanguageModelTextPart(
					`Error retrieving project tags: ${error instanceof Error ? error.message : String(error)}`
				)
			]);
		}
	}

	async prepareInvocation(
		options: vscode.LanguageModelToolInvocationPrepareOptions<IGetProjectTagsToolParams>,
		token: CancellationToken
	): Promise<vscode.PreparedToolInvocation> {
		const { query, type, keywords, tagIds } = options.input;
		
		let message = 'Retrieving project tags';
		
		if (query) {
			message += ` matching "${query}"`;
		} else if (type) {
			message += ` of type "${type}"`;
		} else if (keywords) {
			message += ` with keywords "${keywords}"`;
		} else if (tagIds) {
			message += ` with specific IDs`;
		}
		
		return {
			invocationMessage: message + '...'
		};
	}
}

ToolRegistry.registerTool(GetProjectTagsTool);
