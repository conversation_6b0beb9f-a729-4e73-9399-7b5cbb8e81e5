/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { CancellationToken } from 'vscode';
import { Event } from '../../../util/vs/base/common/event';
import { URI } from '../../../util/vs/base/common/uri';
import { createServiceIdentifier } from '../../../util/common/services';
import {
	IProjectTag,
	IProjectTagsSummary,
	IInitProjectRequest,
	IInitProjectResponse,
	IGetProjectTagsRequest,
	IGetProjectTagsResponse,
	IProjectTagsConfig,
	ProjectTagType
} from './projectTagsTypes';

export const IProjectTagsService = createServiceIdentifier<IProjectTagsService>('IProjectTagsService');

/**
 * Service for managing project tags
 */
export interface IProjectTagsService {
	readonly _serviceBrand: undefined;

	/**
	 * Event fired when tags are updated
	 */
	readonly onDidUpdateTags: Event<IProjectTag[]>;

	/**
	 * Event fired when project initialization completes
	 */
	readonly onDidInitializeProject: Event<IInitProjectResponse>;

	/**
	 * Initialize project tags for the given workspace
	 */
	initializeProject(request: IInitProjectRequest, token: CancellationToken): Promise<IInitProjectResponse>;

	/**
	 * Get project tags based on the request criteria
	 */
	getProjectTags(request: IGetProjectTagsRequest, token: CancellationToken): Promise<IGetProjectTagsResponse>;

	/**
	 * Get a summary of all project tags
	 */
	getProjectSummary(workspaceRoot: URI, token: CancellationToken): Promise<IProjectTagsSummary | undefined>;

	/**
	 * Check if project has been initialized
	 */
	isProjectInitialized(workspaceRoot: URI): Promise<boolean>;

	/**
	 * Update an existing tag
	 */
	updateTag(tag: IProjectTag, token: CancellationToken): Promise<void>;

	/**
	 * Delete a tag
	 */
	deleteTag(tagId: string, token: CancellationToken): Promise<void>;

	/**
	 * Add a new tag
	 */
	addTag(tag: Omit<IProjectTag, 'id' | 'createdAt' | 'updatedAt'>, token: CancellationToken): Promise<IProjectTag>;

	/**
	 * Search tags by query
	 */
	searchTags(query: string, workspaceRoot: URI, token: CancellationToken): Promise<IProjectTag[]>;

	/**
	 * Get tags that are related to the given tag
	 */
	getRelatedTags(tagId: string, token: CancellationToken): Promise<IProjectTag[]>;

	/**
	 * Get configuration for project tags
	 */
	getConfig(): IProjectTagsConfig;

	/**
	 * Update configuration for project tags
	 */
	updateConfig(config: Partial<IProjectTagsConfig>): Promise<void>;

	/**
	 * Clear all tags for a workspace
	 */
	clearTags(workspaceRoot: URI, token: CancellationToken): Promise<void>;

	/**
	 * Export tags to a file
	 */
	exportTags(workspaceRoot: URI, filePath: string, token: CancellationToken): Promise<void>;

	/**
	 * Import tags from a file
	 */
	importTags(workspaceRoot: URI, filePath: string, token: CancellationToken): Promise<IProjectTag[]>;

	/**
	 * Get tags for specific file paths
	 */
	getTagsForFiles(filePaths: string[], workspaceRoot: URI, token: CancellationToken): Promise<IProjectTag[]>;

	/**
	 * Get file paths associated with a tag
	 */
	getFilesForTag(tagId: string, token: CancellationToken): Promise<string[]>;

	/**
	 * Refresh tags by re-analyzing the project
	 */
	refreshTags(workspaceRoot: URI, token: CancellationToken): Promise<IInitProjectResponse>;
}

/**
 * Storage interface for project tags
 */
export interface IProjectTagsStorage {
	readonly _serviceBrand: undefined;

	/**
	 * Save tags to storage
	 */
	saveTags(workspaceRoot: URI, tags: IProjectTag[]): Promise<void>;

	/**
	 * Load tags from storage
	 */
	loadTags(workspaceRoot: URI): Promise<IProjectTag[]>;

	/**
	 * Save project summary to storage
	 */
	saveSummary(workspaceRoot: URI, summary: IProjectTagsSummary): Promise<void>;

	/**
	 * Load project summary from storage
	 */
	loadSummary(workspaceRoot: URI): Promise<IProjectTagsSummary | undefined>;

	/**
	 * Check if tags exist for workspace
	 */
	hasTagsForWorkspace(workspaceRoot: URI): Promise<boolean>;

	/**
	 * Delete all tags for workspace
	 */
	deleteTags(workspaceRoot: URI): Promise<void>;

	/**
	 * Get storage size for workspace
	 */
	getStorageSize(workspaceRoot: URI): Promise<number>;
}

export const IProjectTagsStorage = createServiceIdentifier<IProjectTagsStorage>('IProjectTagsStorage');
