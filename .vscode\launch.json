{
	"version": "0.2.0",
	"configurations": [
		{
			"type": "extensionHost",
			"request": "launch",
			"name": "Launch Copilot Extension",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}"
			],
			"smartStep": true,
			"sourceMaps": true,
			"envFile": "${workspaceFolder}/.env",
			"env": {
				"COPILOT_LOG_TELEMETRY": "true",
				"VSCODE_DEV_DEBUG": "1",
			},
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js",
				"!**/node_modules/**"
			],
			"presentation": {
				"group": "0_launch"
			}
		},
		{
			"type": "extensionHost",
			"request": "launch",
			"name": "Launch Copilot Extension - Watch Mode",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}"
			],
			"smartStep": true,
			"sourceMaps": true,
			"preLaunchTask": "watch",
			"env": {
				"COPILOT_LOG_TELEMETRY": "true",
				"VSCODE_DEV_DEBUG": "1",
			},
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js",
				"!**/node_modules/**"
			],
			"presentation": {
				"group": "0_launch",
			}
		},
		{
			"type": "node",
			"request": "launch",
			"name": "Launch Copilot Extension - Watch Mode - Code OSS",
			"runtimeExecutable": "../../vscode/scripts/code.bat",

			"attachSimplePort": 5870,
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
			],
			"smartStep": true,
			"sourceMaps": true,
			"preLaunchTask": "watch",
			"env": {
				"COPILOT_LOG_TELEMETRY": "true",
				"VSCODE_DEV_DEBUG": "1",
			},
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js",
				"!**/node_modules/**"
			],
			"presentation": {
				"group": "0_launch",
			}
		},
		{
			// See `launchConfigName` in file://./extensions/visualization-runner/extension.ts
			"name": "Test Visualization Runner",
			"type": "node",
			"request": "launch",
			"program": "${workspaceRoot}/test/testVisualizationRunner.ts",
			"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/tsx",
			"presentation": {
				"group": "1_launch"
			},
		},
		{
			// See `launchConfigName` in file://./extensions/test-extension/main.ts
			"name": "Test Visualization Runner STests",
			"type": "node",
			"request": "launch",
			"program": "${workspaceRoot}/test/testVisualizationRunnerSTest.ts",
			"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/tsx",
			"presentation": {
				"group": "1_launch"
			},
		},
		{
			"name": "Extension tests",
			"type": "extensionHost",
			"request": "launch",
			"testConfiguration": "${workspaceFolder}/.vscode-test.mjs",
			"sourceMaps": true,
			"smartStep": true,
			"envFile": "${workspaceFolder}/.env",
			"internalConsoleOptions": "openOnSessionStart",
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js",
				"!**/node_modules/**"
			],
			"presentation": {
				"group": "1_launch"
			},
		},
		{
			"name": "Run simulation tests",
			"internalConsoleOptions": "openOnSessionStart",
			"runtimeExecutable": "npm",
			"args": [
				"run",
				"simulate",
				"--",
				"-n=1",
				"-p=1"
			],
			"request": "launch",
			"type": "node",
			"presentation": {
				"group": "1_launch"
			}
		},
		{
			"name": "Run tool call s-test",
			"internalConsoleOptions": "openOnSessionStart",
			"runtimeExecutable": "npm",
			"args": [
				"run",
				"simulate",
				"--",
				"--verbose",
				"-n=1",
				"-p=1",
				"--sidebar",
				"--scenario-test=toolcall.stest",
				"--external-scenarios=${workspaceFolder}/test/toolcalls",
				"--output=${workspaceFolder}/test/out/"

			],
			"request": "launch",
			"type": "node",
			"presentation": {
				"group": "1_launch"
			}
		},
		{
			"type": "node",
			"name": "Postinstall",
			"request": "launch",
			"runtimeExecutable": "npm",
			"args": [
				"run",
				"postinstall"
			],
			"presentation": {
				"group": "1_launch"
			}
		},
		{
			"name": "Debug Simulation Workbench UI",
			"type": "chrome",
			"request": "launch",
			"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron",
			"windows": {
				"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron.cmd"
			},
			"runtimeArgs": [
				"${workspaceRoot}/script/electron/simulationWorkbenchMain.js",
				"--remote-debugging-port=9222"
			],
			"webRoot": "${workspaceRoot}",
			"presentation": {
				"group": "1_launch"
			}
		},
		{
			"name": "Simulation Workbench - Renderer process",
			"type": "chrome",
			"request": "attach",
			"webRoot": "${workspaceFolder}",
			"port": 9222,
			"presentation": {
				"group": "1_launch"
			},
		},
		{
			"name": "Simulation Workbench - Main process",
			"type": "node",
			"request": "launch",
			"cwd": "${workspaceFolder}",
			"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron",
			"windows": {
				"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"
			},
			"args": [
				"${workspaceRoot}/script/electron/simulationWorkbenchMain.js",
				"--remote-debugging-port=9222"
			],
			"autoAttachChildProcesses": true,
			"presentation": {
				"group": "1_launch"
			},
		},
		{
			"type": "node",
			"request": "launch",
			"name": "Debug Test Script",
			"runtimeExecutable": "npm",
			"args": [
				"run",
				"test"
			],
			"console": "integratedTerminal",
			"internalConsoleOptions": "neverOpen"
		},
		{
			"type": "extensionHost",
			"request": "launch",
			"name": "Launch Copilot Extension - TS Server in Debug Mode",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}"
			],
			"smartStep": true,
			"sourceMaps": true,
			"envFile": "${workspaceFolder}/.env",
			"env": {
				"COPILOT_LOG_TELEMETRY": "true",
				"TSS_REMOTE_DEBUG": "9223",
				"TSS_DEBUG": "9223",
			},
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js",
				"!**/node_modules/**"
			],
			"presentation": {
				"group": "2_launch"
			}
		},
		{
			"name": "Attach to TypeScript Server",
			"type": "node",
			"request": "attach",
			"port": 9223,
			"outFiles": [
				"${workspaceFolder}/node_modules/@vscode/copilot-typescript-server-plugin/dist/**/*.js",
			],
			"presentation": {
				"group": "2_launch"
			}
		}
	],
	"compounds": [
		{
			"name": "Simulation Workbench",
			"configurations": [
				"Simulation Workbench - Main process",
				"Simulation Workbench - Renderer process"
			],
			"stopAll": true,
			"presentation": {
				"group": "1_launch"
			}
		}
	]
}