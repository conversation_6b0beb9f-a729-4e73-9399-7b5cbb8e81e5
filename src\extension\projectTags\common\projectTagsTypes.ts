/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../util/vs/base/common/uri';

/**
 * Represents a tag for a specific part of the project
 */
export interface IProjectTag {
	/** Unique identifier for the tag */
	id: string;
	/** Human-readable name of the tag */
	name: string;
	/** Description of what this tag represents */
	description: string;
	/** Type of the tagged entity */
	type: ProjectTagType;
	/** File paths associated with this tag */
	filePaths: string[];
	/** Key concepts or keywords associated with this tag */
	keywords: string[];
	/** Dependencies on other tags */
	dependencies: string[];
	/** Relative importance/priority (1-10) */
	priority: number;
	/** When this tag was created */
	createdAt: Date;
	/** When this tag was last updated */
	updatedAt: Date;
	/** Additional metadata */
	metadata?: Record<string, any>;
}

/**
 * Types of project entities that can be tagged
 */
export enum ProjectTagType {
	/** Core architectural component */
	Architecture = 'architecture',
	/** Feature or functionality */
	Feature = 'feature',
	/** Service or module */
	Service = 'service',
	/** Data model or schema */
	DataModel = 'dataModel',
	/** API or interface */
	API = 'api',
	/** Configuration */
	Configuration = 'configuration',
	/** Testing related */
	Testing = 'testing',
	/** Documentation */
	Documentation = 'documentation',
	/** Build/deployment */
	Build = 'build',
	/** External dependency */
	Dependency = 'dependency',
	/** Utility or helper */
	Utility = 'utility'
}

/**
 * Summary of all project tags
 */
export interface IProjectTagsSummary {
	/** Total number of tags */
	totalTags: number;
	/** Tags by type */
	tagsByType: Record<ProjectTagType, number>;
	/** Most important tags (top 10) */
	topTags: IProjectTag[];
	/** Recently updated tags */
	recentlyUpdated: IProjectTag[];
	/** Project overview generated by LLM */
	projectOverview: string;
	/** When the summary was generated */
	generatedAt: Date;
}

/**
 * Request to initialize project tags
 */
export interface IInitProjectRequest {
	/** Workspace root URI */
	workspaceRoot: URI;
	/** Whether to force re-initialization */
	force?: boolean;
	/** Maximum number of tags to generate */
	maxTags?: number;
	/** Specific directories to focus on */
	focusDirectories?: string[];
	/** File patterns to exclude */
	excludePatterns?: string[];
}

/**
 * Response from project initialization
 */
export interface IInitProjectResponse {
	/** Whether initialization was successful */
	success: boolean;
	/** Generated tags */
	tags: IProjectTag[];
	/** Project summary */
	summary: IProjectTagsSummary;
	/** Any errors encountered */
	errors?: string[];
	/** Time taken for initialization */
	duration: number;
}

/**
 * Request to get specific project tags
 */
export interface IGetProjectTagsRequest {
	/** Specific tag IDs to retrieve */
	tagIds?: string[];
	/** Filter by tag type */
	type?: ProjectTagType;
	/** Filter by keywords */
	keywords?: string[];
	/** Maximum number of tags to return */
	limit?: number;
	/** Include file contents in response */
	includeContent?: boolean;
}

/**
 * Response with project tags
 */
export interface IGetProjectTagsResponse {
	/** Retrieved tags */
	tags: IProjectTag[];
	/** File contents if requested */
	fileContents?: Record<string, string>;
	/** Total number of matching tags */
	totalMatching: number;
}

/**
 * Configuration for project tags system
 */
export interface IProjectTagsConfig {
	/** Whether project tags are enabled */
	enabled: boolean;
	/** Auto-initialize on workspace open */
	autoInitialize: boolean;
	/** Maximum number of tags to store */
	maxTags: number;
	/** Cache duration in milliseconds */
	cacheDuration: number;
	/** File patterns to exclude from tagging */
	excludePatterns: string[];
	/** Minimum file size to consider for tagging */
	minFileSize: number;
	/** Maximum file size to consider for tagging */
	maxFileSize: number;
}
