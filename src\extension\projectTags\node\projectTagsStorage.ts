/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as path from 'path';
import { URI } from '../../../util/vs/base/common/uri';
import { IFileSystemService } from '../../../platform/filesystem/common/fileSystemService';
import { IVSCodeExtensionContext } from '../../../platform/extContext/common/extContext';
import { IProjectTagsStorage } from '../common/projectTagsService';
import { IProjectTag, IProjectTagsSummary } from '../common/projectTagsTypes';

/**
 * File-based storage implementation for project tags
 */
export class ProjectTagsStorage implements IProjectTagsStorage {
	readonly _serviceBrand: undefined;

	private static readonly TAGS_FILE_NAME = 'project-tags.json';
	private static readonly SUMMARY_FILE_NAME = 'project-summary.json';
	private static readonly STORAGE_VERSION = '1.0.0';

	constructor(
		@IFileSystemService private readonly fileSystemService: IFileSystemService,
		@IVSCodeExtensionContext private readonly extensionContext: IVSCodeExtensionContext
	) {}

	private getStorageDir(workspaceRoot: URI): URI {
		// Create a unique directory name based on workspace path
		const workspaceHash = this.hashWorkspacePath(workspaceRoot.fsPath);
		return URI.joinPath(
			this.extensionContext.globalStorageUri,
			'projectTags',
			workspaceHash
		);
	}

	private hashWorkspacePath(path: string): string {
		// Simple hash function for workspace path
		let hash = 0;
		for (let i = 0; i < path.length; i++) {
			const char = path.charCodeAt(i);
			hash = ((hash << 5) - hash) + char;
			hash = hash & hash; // Convert to 32-bit integer
		}
		return Math.abs(hash).toString(36);
	}

	private getTagsFilePath(workspaceRoot: URI): URI {
		return URI.joinPath(this.getStorageDir(workspaceRoot), ProjectTagsStorage.TAGS_FILE_NAME);
	}

	private getSummaryFilePath(workspaceRoot: URI): URI {
		return URI.joinPath(this.getStorageDir(workspaceRoot), ProjectTagsStorage.SUMMARY_FILE_NAME);
	}

	async saveTags(workspaceRoot: URI, tags: IProjectTag[]): Promise<void> {
		const storageDir = this.getStorageDir(workspaceRoot);
		const tagsFile = this.getTagsFilePath(workspaceRoot);

		// Ensure storage directory exists
		await this.fileSystemService.createDirectory(storageDir);

		const data = {
			version: ProjectTagsStorage.STORAGE_VERSION,
			workspaceRoot: workspaceRoot.toString(),
			savedAt: new Date().toISOString(),
			tags: tags.map(tag => ({
				...tag,
				createdAt: tag.createdAt.toISOString(),
				updatedAt: tag.updatedAt.toISOString()
			}))
		};

		const content = JSON.stringify(data, null, 2);
		await this.fileSystemService.writeFile(tagsFile, Buffer.from(content, 'utf8'));
	}

	async loadTags(workspaceRoot: URI): Promise<IProjectTag[]> {
		const tagsFile = this.getTagsFilePath(workspaceRoot);

		try {
			const exists = await this.fileSystemService.exists(tagsFile);
			if (!exists) {
				return [];
			}

			const content = await this.fileSystemService.readFile(tagsFile);
			const data = JSON.parse(content.toString('utf8'));

			// Validate version compatibility
			if (data.version !== ProjectTagsStorage.STORAGE_VERSION) {
				console.warn(`Project tags storage version mismatch. Expected ${ProjectTagsStorage.STORAGE_VERSION}, got ${data.version}`);
				return [];
			}

			// Convert date strings back to Date objects
			return data.tags.map((tag: any) => ({
				...tag,
				createdAt: new Date(tag.createdAt),
				updatedAt: new Date(tag.updatedAt)
			}));
		} catch (error) {
			console.error('Failed to load project tags:', error);
			return [];
		}
	}

	async saveSummary(workspaceRoot: URI, summary: IProjectTagsSummary): Promise<void> {
		const storageDir = this.getStorageDir(workspaceRoot);
		const summaryFile = this.getSummaryFilePath(workspaceRoot);

		// Ensure storage directory exists
		await this.fileSystemService.createDirectory(storageDir);

		const data = {
			version: ProjectTagsStorage.STORAGE_VERSION,
			workspaceRoot: workspaceRoot.toString(),
			savedAt: new Date().toISOString(),
			summary: {
				...summary,
				generatedAt: summary.generatedAt.toISOString(),
				topTags: summary.topTags.map(tag => ({
					...tag,
					createdAt: tag.createdAt.toISOString(),
					updatedAt: tag.updatedAt.toISOString()
				})),
				recentlyUpdated: summary.recentlyUpdated.map(tag => ({
					...tag,
					createdAt: tag.createdAt.toISOString(),
					updatedAt: tag.updatedAt.toISOString()
				}))
			}
		};

		const content = JSON.stringify(data, null, 2);
		await this.fileSystemService.writeFile(summaryFile, Buffer.from(content, 'utf8'));
	}

	async loadSummary(workspaceRoot: URI): Promise<IProjectTagsSummary | undefined> {
		const summaryFile = this.getSummaryFilePath(workspaceRoot);

		try {
			const exists = await this.fileSystemService.exists(summaryFile);
			if (!exists) {
				return undefined;
			}

			const content = await this.fileSystemService.readFile(summaryFile);
			const data = JSON.parse(content.toString('utf8'));

			// Validate version compatibility
			if (data.version !== ProjectTagsStorage.STORAGE_VERSION) {
				console.warn(`Project summary storage version mismatch. Expected ${ProjectTagsStorage.STORAGE_VERSION}, got ${data.version}`);
				return undefined;
			}

			// Convert date strings back to Date objects
			const summary = data.summary;
			return {
				...summary,
				generatedAt: new Date(summary.generatedAt),
				topTags: summary.topTags.map((tag: any) => ({
					...tag,
					createdAt: new Date(tag.createdAt),
					updatedAt: new Date(tag.updatedAt)
				})),
				recentlyUpdated: summary.recentlyUpdated.map((tag: any) => ({
					...tag,
					createdAt: new Date(tag.createdAt),
					updatedAt: new Date(tag.updatedAt)
				}))
			};
		} catch (error) {
			console.error('Failed to load project summary:', error);
			return undefined;
		}
	}

	async hasTagsForWorkspace(workspaceRoot: URI): Promise<boolean> {
		const tagsFile = this.getTagsFilePath(workspaceRoot);
		return await this.fileSystemService.exists(tagsFile);
	}

	async deleteTags(workspaceRoot: URI): Promise<void> {
		const storageDir = this.getStorageDir(workspaceRoot);
		try {
			await this.fileSystemService.delete(storageDir, { recursive: true, useTrash: false });
		} catch (error) {
			// Ignore errors if directory doesn't exist
			console.warn('Failed to delete project tags storage:', error);
		}
	}

	async getStorageSize(workspaceRoot: URI): Promise<number> {
		const storageDir = this.getStorageDir(workspaceRoot);
		try {
			const stat = await this.fileSystemService.stat(storageDir);
			return stat.size;
		} catch (error) {
			return 0;
		}
	}
}
