/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as vscode from 'vscode';
import { CancellationToken, LanguageModelToolInvocationOptions, LanguageModelToolResult } from 'vscode';
import { LanguageModelTextPart } from '../../../util/vs/base/common/languageModels';
import { checkCancellation } from '../../../util/vs/base/common/cancellation';
import { IWorkspaceService } from '../../../platform/workspace/common/workspaceService';
import { IProjectTagsService } from '../../projectTags/common/projectTagsService';
import { IInitProjectRequest, ProjectTagType } from '../../projectTags/common/projectTagsTypes';
import { ToolName } from '../common/toolNames';
import { ToolRegistry } from '../common/toolsRegistry';
import { URI } from '../../../util/vs/base/common/uri';

interface IInitProjectToolParams {
	/** Whether to force re-initialization even if tags already exist */
	force?: boolean;
	/** Maximum number of tags to generate */
	maxTags?: number;
	/** Specific directories to focus on (relative to workspace root) */
	focusDirectories?: string[];
	/** File patterns to exclude from analysis */
	excludePatterns?: string[];
}

/**
 * Tool for initializing project tags by analyzing the workspace structure
 */
class InitProjectTool implements vscode.LanguageModelTool<IInitProjectToolParams> {
	public static readonly toolName = ToolName.InitProject;

	constructor(
		@IWorkspaceService private readonly workspaceService: IWorkspaceService,
		@IProjectTagsService private readonly projectTagsService: IProjectTagsService
	) {}

	async invoke(
		options: LanguageModelToolInvocationOptions<IInitProjectToolParams>,
		token: CancellationToken
	): Promise<LanguageModelToolResult> {
		checkCancellation(token);

		const { force = false, maxTags = 50, focusDirectories, excludePatterns } = options.input;

		// Get the current workspace
		const workspaceFolders = this.workspaceService.workspaceFolders;
		if (!workspaceFolders || workspaceFolders.length === 0) {
			return new LanguageModelToolResult([
				new LanguageModelTextPart('No workspace is currently open. Please open a workspace first.')
			]);
		}

		const workspaceRoot = workspaceFolders[0].uri;

		try {
			// Check if already initialized
			if (!force) {
				const isInitialized = await this.projectTagsService.isProjectInitialized(workspaceRoot);
				if (isInitialized) {
					const summary = await this.projectTagsService.getProjectSummary(workspaceRoot, token);
					if (summary) {
						return new LanguageModelToolResult([
							new LanguageModelTextPart(
								`Project is already initialized with ${summary.totalTags} tags. ` +
								`Use force=true to re-initialize.\n\n` +
								`Project Overview: ${summary.projectOverview}\n\n` +
								`Top Tags: ${summary.topTags.map(t => `${t.name} (${t.type})`).join(', ')}`
							)
						]);
					}
				}
			}

			// Create initialization request
			const request: IInitProjectRequest = {
				workspaceRoot,
				force,
				maxTags,
				focusDirectories,
				excludePatterns
			};

			// Initialize the project
			const response = await this.projectTagsService.initializeProject(request, token);

			if (!response.success) {
				const errors = response.errors?.join('\n') || 'Unknown error occurred';
				return new LanguageModelToolResult([
					new LanguageModelTextPart(`Failed to initialize project tags:\n${errors}`)
				]);
			}

			// Format the response
			const { tags, summary, duration } = response;
			
			let result = `Successfully initialized project with ${tags.length} tags in ${duration}ms.\n\n`;
			result += `Project Overview:\n${summary.projectOverview}\n\n`;
			
			result += `Tags by Type:\n`;
			for (const [type, count] of Object.entries(summary.tagsByType)) {
				if (count > 0) {
					result += `- ${type}: ${count}\n`;
				}
			}
			
			result += `\nTop Priority Tags:\n`;
			summary.topTags.slice(0, 10).forEach((tag, index) => {
				result += `${index + 1}. ${tag.name} (${tag.type}) - Priority: ${tag.priority}\n`;
				result += `   Description: ${tag.description}\n`;
				if (tag.keywords.length > 0) {
					result += `   Keywords: ${tag.keywords.join(', ')}\n`;
				}
				result += '\n';
			});

			if (tags.length > 10) {
				result += `\n... and ${tags.length - 10} more tags.\n`;
			}

			result += `\nYou can now use the @getProjectTags tool to query specific tags and their content.`;

			return new LanguageModelToolResult([
				new LanguageModelTextPart(result)
			]);

		} catch (error) {
			return new LanguageModelToolResult([
				new LanguageModelTextPart(
					`Error initializing project: ${error instanceof Error ? error.message : String(error)}`
				)
			]);
		}
	}

	async prepareInvocation(
		options: vscode.LanguageModelToolInvocationPrepareOptions<IInitProjectToolParams>,
		token: CancellationToken
	): Promise<vscode.PreparedToolInvocation> {
		const workspaceFolders = this.workspaceService.workspaceFolders;
		const workspaceName = workspaceFolders?.[0]?.name || 'workspace';
		
		return {
			invocationMessage: `Analyzing ${workspaceName} to generate project tags...`
		};
	}
}

ToolRegistry.registerTool(InitProjectTool);
